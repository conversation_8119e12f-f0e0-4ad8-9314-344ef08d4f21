using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Xml;

namespace SiaSun.LMS.Implement.Interface.iWMS
{
    /// <summary>
    /// 出库单完成上报回调接口 【iWMS提供，SSWMS调用】
    /// 注册(regist) -> 获取凭证(getCertificate) -> 携带凭证调用 accessInterface(OutboundReceiptCompleteCallback)
    /// </summary>
    public class OutboundReceiptCompleteCallback : InterfaceBase
    {
        /// <summary>
        /// 访问接口的Response结构
        /// </summary>
        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string data { get; set; }
        }

        /// <summary>
        /// 出库单完成上报明细项
        /// </summary>
        public class OutboundReceiptCompleteItem
        {
            /// <summary>
            /// 数量
            /// </summary>
            public int goodsNum { get; set; }

            /// <summary>
            /// 仓库编码
            /// </summary>
            public string warehouseCode { get; set; }

            /// <summary>
            /// 货架编码
            /// </summary>
            public string shelfCode { get; set; }

            /// <summary>
            /// 出库明细原id ID
            /// </summary>
            public string oId { get; set; }

            /// <summary>
            /// 立体仓系统：出库明细id
            /// </summary>
            public string lId { get; set; }

            /// <summary>
            /// 出库类型：63:出库单，72:入库红冲单，74:借用
            /// </summary>
            public int outboundType { get; set; }
        }

        /// <summary>
        /// 调用外部物资系统出库单完成上报（单个明细）
        /// </summary>
        /// <param name="goodsNum">数量</param>
        /// <param name="warehouseCode">仓库编码</param>
        /// <param name="shelfCode">货架编码</param>
        /// <param name="oId">出库明细原id ID</param>
        /// <param name="lId">立体仓系统：出库明细id</param>
        /// <param name="outboundType">出库类型：63:出库单，72:入库红冲单，74:借用</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        public bool IntefaceMethod(int goodsNum, string warehouseCode, string shelfCode, 
            string oId, string lId, int outboundType, out string message)
        {
            var items = new List<OutboundReceiptCompleteItem>
            {
                new OutboundReceiptCompleteItem
                {
                    goodsNum = goodsNum,
                    warehouseCode = warehouseCode,
                    shelfCode = shelfCode,
                    oId = oId,
                    lId = lId,
                    outboundType = outboundType
                }
            };

            return IntefaceMethod(items, out message);
        }

        /// <summary>
        /// 调用外部物资系统出库单完成上报（批量明细）
        /// </summary>
        /// <param name="outboundItems">出库明细列表</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        public bool IntefaceMethod(List<OutboundReceiptCompleteItem> outboundItems, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                // 基础校验
                if (outboundItems == null || outboundItems.Count == 0)
                {
                    result = false;
                    message = "参数错误：outboundItems 不能为空";
                    return result;
                }

                // 校验每个明细项的必填字段
                for (int i = 0; i < outboundItems.Count; i++)
                {
                    var item = outboundItems[i];
                    if (string.IsNullOrEmpty(item.warehouseCode))
                    {
                        result = false;
                        message = $"参数错误：第{i + 1}项的warehouseCode不能为空";
                        return result;
                    }
                    if (string.IsNullOrEmpty(item.shelfCode))
                    {
                        result = false;
                        message = $"参数错误：第{i + 1}项的shelfCode不能为空";
                        return result;
                    }
                    if (string.IsNullOrEmpty(item.oId))
                    {
                        result = false;
                        message = $"参数错误：第{i + 1}项的oId不能为空";
                        return result;
                    }
                    if (string.IsNullOrEmpty(item.lId))
                    {
                        result = false;
                        message = $"参数错误：第{i + 1}项的lId不能为空";
                        return result;
                    }
                    if (item.outboundType != 63 && item.outboundType != 72 && item.outboundType != 74)
                    {
                        result = false;
                        message = $"参数错误：第{i + 1}项的outboundType必须为63(出库单)、72(入库红冲单)或74(借用)";
                        return result;
                    }
                }

                // Step 1: 注册，获取secrit
                string registInput = Common.JsonHelper.Serializer(new { systemIdentify = "ZYK" });
                if (!InvokeExternal("regist", registInput, out string registOut))
                {
                    result = false;
                    message = $"调用regist失败：{registOut}";
                    return result;
                }

                var registResp = TryParse<OutputParam>(registOut, out string parseErr1);
                if (registResp == null || registResp.code != 200 || string.IsNullOrEmpty(registResp.data))
                {
                    result = false;
                    message = $"regist返回无效：{(registResp == null ? parseErr1 : registResp.msg)}";
                    return result;
                }

                string secrit = registResp.data;

                // Step 2: 获取凭证 certificate
                var applyTokenInputObj = new
                {
                    interfaceIdentify = "outGoodsCallback",
                    publicKey = secrit,
                    systemIdentify = "ZYK",
                    tokenExpired = "2"
                };
                string applyTokenInput = Common.JsonHelper.Serializer(applyTokenInputObj);
                if (!InvokeExternal("getCertificate", applyTokenInput, out string applyTokenOut))
                {
                    result = false;
                    message = $"调用getCertificate失败：{applyTokenOut}";
                    return result;
                }

                var applyResp = TryParse<OutputParam>(applyTokenOut, out string parseErr2);
                if (applyResp == null || applyResp.code != 200 || string.IsNullOrEmpty(applyResp.data))
                {
                    result = false;
                    message = $"getCertificate返回无效：{(applyResp == null ? parseErr2 : applyResp.msg)}";
                    return result;
                }

                string certificate = applyResp.data;

                // Step 3: 组织业务负载并调用 accessInterface(OutboundReceiptCompleteCallback)
                string payloadJson = Common.JsonHelper.Serializer(outboundItems);

                // 按Java参考实现，accessInterface 的 paramsJSONStr 结构
                string paramsJSONStr = Common.JsonHelper.Serializer(new
                {
                    systemIdentify = "ZYK",
                    interfaceIdentify = "outGoodsCallback",
                    certificate = certificate,
                    json = payloadJson
                });

                string accessInterfaceInput = paramsJSONStr;
                if (!InvokeExternal("accessInterface", accessInterfaceInput, out string accessInterfaceOut))
                {
                    result = false;
                    message = $"调用accessInterface失败：{accessInterfaceOut}";
                    return result;
                }

                var accessResp = TryParse<OutputParam>(accessInterfaceOut, out string parseErr3);
                if (accessResp == null || accessResp.code != 200)
                {
                    result = false;
                    message = $"accessInterface返回无效：{(accessResp == null ? parseErr3 : accessResp.msg)}";
                    return result;
                }

                message = "出库单完成上报成功";
                S_Base.sBase.Log.Info($"OutboundReceiptCompleteCallback成功_明细数[{outboundItems.Count}]_traceId[{Guid.NewGuid()}]_信息[{message}]");
            }
            catch (Exception ex)
            {
                result = false;
                message = $"异常_信息[{ex.Message}]";
                S_Base.sBase.Log.Error($"OutboundReceiptCompleteCallback异常：{ex.Message}", ex);
            }

            return result;
        }

        private T TryParse<T>(string jsonOrWrapped, out string error) where T : class
        {
            error = null;
            try
            {
                // 某些WebService返回SOAP中包含<String>{json}</String>，
                // 若传入已是纯json字符串也可直接解析
                string candidate = jsonOrWrapped;
                // 提取可能包裹的XML中的<String>内容
                if (!string.IsNullOrEmpty(candidate) && candidate.Contains("<"))
                {
                    try
                    {
                        var xml = new XmlDocument();
                        xml.LoadXml(candidate);
                        var node = xml.SelectSingleNode("//String");
                        if (node != null)
                        {
                            candidate = node.InnerText;
                        }
                    }
                    catch { /* ignore xml parse */ }
                }
                return Common.JsonHelper.Deserialize<T>(candidate);
            }
            catch (Exception ex)
            {
                error = ex.Message;
                return null;
            }
        }


    }
}
