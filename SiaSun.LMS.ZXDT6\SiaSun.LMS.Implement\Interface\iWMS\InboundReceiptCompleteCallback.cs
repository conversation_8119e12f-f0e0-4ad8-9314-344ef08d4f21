using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Xml;

namespace SiaSun.LMS.Implement.Interface.iWMS
{
    /// <summary>
    /// 入库单完成上报回调接口 【iWMS提供，SSWMS调用】
    /// 注册(regist) -> 获取凭证(getCertificate) -> 携带凭证调用 accessInterface(InboundReceiptCompleteCallback)
    /// </summary>
    public class InboundReceiptCompleteCallback : InterfaceBase
    {

        /// <summary>
        /// 访问接口的Response结构
        /// </summary>
        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string data { get; set; }
        }

        /// <summary>
        /// 入库单完成上报明细项
        /// </summary>
        public class InboundReceiptCompleteItem
        {
            /// <summary>
            /// 本次入库数量
            /// </summary>
            public int storageNum { get; set; }

            /// <summary>
            /// 仓库编码
            /// </summary>
            public string warehouseCode { get; set; }

            /// <summary>
            /// 货架编码
            /// </summary>
            public string shelfCode { get; set; }

            /// <summary>
            /// 入库明细原ID
            /// </summary>
            public string oId { get; set; }

            /// <summary>
            /// 立体仓系统：入库明细ID
            /// </summary>
            public string lId { get; set; }

            /// <summary>
            /// 入库类型：28:入库单，73:出库红冲单，75:归还单
            /// </summary>
            public int storageType { get; set; }
        }

        /// <summary>
        /// 调用外部物资系统入库单完成上报回调。
        /// </summary>
        /// <param name="inboundReceiptCompleteItems">入库单完成明细列表</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        public bool IntefaceMethod(List<InboundReceiptCompleteItem> inboundReceiptCompleteItems, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                // 基础校验
                if (inboundReceiptCompleteItems == null || inboundReceiptCompleteItems.Count == 0)
                {
                    result = false;
                    message = "参数错误：inboundReceiptCompleteItems 不能为空";
                    return result;
                }

                // 验证每个明细项的必要字段
                foreach (var item in inboundReceiptCompleteItems)
                {
                    if (string.IsNullOrEmpty(item.warehouseCode) || string.IsNullOrEmpty(item.shelfCode) ||
                        string.IsNullOrEmpty(item.oId) || string.IsNullOrEmpty(item.lId))
                    {
                        result = false;
                        message = "参数错误：明细项中的warehouseCode、shelfCode、oId、lId不能为空";
                        return result;
                    }

                    if (item.storageType != 28 && item.storageType != 73 && item.storageType != 75)
                    {
                        result = false;
                        message = "参数错误：storageType必须为28(入库单)、73(出库红冲单)或75(归还单)";
                        return result;
                    }
                }

                // Step 1: 注册，获取secrit
                string registInput = Common.JsonHelper.Serializer(new { systemIdentify = "ZYK" });
                if (!InvokeExternal("regist", registInput, out string registOut))
                {
                    result = false;
                    message = $"调用regist失败：{registOut}";
                    return result;
                }


                var registResp = TryParse<OutputParam>(registOut, out string parseErr1);
                if (registResp == null || registResp.code != 200 || string.IsNullOrEmpty(registResp.data))
                {
                    result = false;
                    message = $"regist返回无效：{(registResp == null ? parseErr1 : registResp.msg)}";
                    return result;
                }

                string secrit = registResp.data;

                // Step 2: 获取凭证 certificate
                var applyTokenInputObj = new
                {
                    interfaceIdentify = "storageCallback",
                    publicKey = secrit,
                    systemIdentify = "ZYK",
                    tokenExpired = "2"
                };
                string applyTokenInput = Common.JsonHelper.Serializer(applyTokenInputObj);
                if (!InvokeExternal("getCertificate", applyTokenInput, out string applyTokenOut))
                {
                    result = false;
                    message = $"调用getCertificate失败：{applyTokenOut}";
                    return result;
                }

                var applyResp = TryParse<OutputParam>(applyTokenOut, out string parseErr2);
                if (applyResp == null || applyResp.code != 200 || string.IsNullOrEmpty(applyResp.data))
                {
                    result = false;
                    message = $"getCertificate返回无效：{(applyResp == null ? parseErr2 : applyResp.msg)}";
                    return result;
                }

                string certificate = applyResp.data;

                // Step 3: 组织业务负载并调用 accessInterface(InboundReceiptCompleteCallback)
                string payloadJson = Common.JsonHelper.Serializer(inboundReceiptCompleteItems);

                // 按InventoryResultCallback参考实现，accessInterface 的 paramsJSONStr 结构
                string paramsJSONStr = Common.JsonHelper.Serializer(new
                {
                    systemIdentify = "ZYK",
                    interfaceIdentify = "storageCallback",
                    certificate = certificate,
                    json = payloadJson
                });

                string accessInterfaceInput = paramsJSONStr;
                if (!InvokeExternal("accessInterface", accessInterfaceInput, out string accessInterfaceOut))
                {
                    result = false;
                    message = $"调用accessInterface失败：{accessInterfaceOut}";
                    return result;
                }

                var accessResp = TryParse<OutputParam>(accessInterfaceOut, out string parseErr3);
                if (accessResp == null || accessResp.code != 200)
                {
                    result = false;
                    message = $"accessInterface返回无效：{(accessResp == null ? parseErr3 : accessResp.msg)}";
                    return result;
                }

                message = "入库单完成上报成功";
                S_Base.sBase.Log.Info($"InboundReceiptCompleteCallback成功_明细数[{inboundReceiptCompleteItems.Count}]_traceId[{Guid.NewGuid()}]_信息[{message}]");
            }
            catch (Exception ex)
            {
                result = false;
                message = $"异常_信息[{ex.Message}]";
                S_Base.sBase.Log.Error($"InboundReceiptCompleteCallback异常：{ex.Message}", ex);
            }

            return result;
        }

        /// <summary>
        /// 重载方法：支持单个入库单明细上报
        /// </summary>
        /// <param name="storageNum">本次入库数量</param>
        /// <param name="warehouseCode">仓库编码</param>
        /// <param name="shelfCode">货架编码</param>
        /// <param name="oId">入库明细原ID</param>
        /// <param name="lId">立体仓系统入库明细ID</param>
        /// <param name="storageType">入库类型</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        public bool IntefaceMethod(int storageNum, string warehouseCode, string shelfCode, 
            string oId, string lId, int storageType, out string message)
        {
            var items = new List<InboundReceiptCompleteItem>
            {
                new InboundReceiptCompleteItem
                {
                    storageNum = storageNum,
                    warehouseCode = warehouseCode,
                    shelfCode = shelfCode,
                    oId = oId,
                    lId = lId,
                    storageType = storageType
                }
            };

            return IntefaceMethod(items, out message);
        }

        private T TryParse<T>(string jsonOrWrapped, out string error) where T : class
        {
            error = null;
            try
            {
                // 某些WebService返回SOAP中包含<String>{json}</String>，
                // 若传入已是纯json字符串也可直接解析
                string candidate = jsonOrWrapped;
                // 提取可能包裹的XML中的<String>内容
                if (!string.IsNullOrEmpty(candidate) && candidate.Contains("<"))
                {
                    try
                    {
                        var xml = new XmlDocument();
                        xml.LoadXml(candidate);
                        var node = xml.SelectSingleNode("//String");
                        if (node != null)
                        {
                            candidate = node.InnerText;
                        }
                    }
                    catch { /* ignore xml parse */ }
                }
                return Common.JsonHelper.Deserialize<T>(candidate);
            }
            catch (Exception ex)
            {
                error = ex.Message;
                return null;
            }
        }

        private string GetExternalServiceUrl()
        {
            // InterfaceBase中externalServiceUrl是private静态，这里从配置读取同名键
            return SiaSun.LMS.Common.StringUtil.GetConfig("ExternalServiceUrl");
        }
    }
}
